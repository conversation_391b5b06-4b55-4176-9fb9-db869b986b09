'use client';

import { useGoogleLogin } from '@react-oauth/google';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Button, CircularProgress } from '@mui/material';
import GoogleIcon from '@mui/icons-material/Google';

interface GoogleLoginButtonProps {
  text?: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

export default function GoogleLoginButton({
  text = 'Sign in with Google',
  onSuccess,
  onError,
  className = '',
}: GoogleLoginButtonProps) {
  const { googleLogin } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: async (codeResponse) => {
      setIsLoading(true);
      try {
        const result = await googleLogin({
          code: codeResponse.code,
          redirectUri: window.location.origin
        });

        if (result.succeeded) {
          if (onSuccess) {
            onSuccess();
          } else {
            // Check if user is SuperAdmin and redirect accordingly
            if (result.roles?.includes('SuperAdmin')) {
              router.push('/super-admin/dashboard');
            } else {
              router.push('/dashboard');
            }
          }
        } else {
          if (onError) {
            onError(result.errors?.join(', ') || 'Google login failed');
          }
        }
      } catch (error) {
        console.error('Google login error:', error);
        if (onError) {
          onError('An unexpected error occurred');
        }
      } finally {
        setIsLoading(false);
      }
    },
    flow: 'auth-code',
    onError: (error) => {
      console.error('Google login error:', error);
      if (onError) {
        onError('Google login failed');
      }
    },
  });

  return (
    <Button
      onClick={() => handleGoogleLogin()}
      disabled={isLoading}
      variant="outlined"
      fullWidth
      startIcon={isLoading ? <CircularProgress size={20} /> : <GoogleIcon />}
      sx={{
        textTransform: 'none',
        py: 1,
        backgroundColor: 'white',
        '&:hover': {
          backgroundColor: '#f8f9fa'
        }
      }}
    >
      {text}
    </Button>
  );
}
