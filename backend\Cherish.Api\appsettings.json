{"Database": {"Provider": "postgresql"}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=cherish;Username=postgres;Password=*************"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Key": "YourSuperSecretKeyHereMakeItLongAndComplex", "Issuer": "Cherish", "Audience": "CherishClients", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "Authentication": {"Google": {"ClientId": "27179029615-0n6opo3ej6vj8u37t0pot019kdbgdnge.apps.googleusercontent.com", "ClientSecret": "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"}}}