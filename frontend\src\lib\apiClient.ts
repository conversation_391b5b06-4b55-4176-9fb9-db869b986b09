import axios from 'axios';
import Cookies from 'js-cookie';

const baseURL = process.env.NEXT_PUBLIC_API_URL || '/api';

export const apiClient = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Get token from cookie
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // If the error is due to an expired token (401) and we haven't tried to refresh yet
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = Cookies.get('refreshToken');

        if (!refreshToken) {
          // No refresh token, redirect to home page
          Cookies.remove('token');
          window.location.href = '/';
          return Promise.reject(error);
        }

        // Try to refresh the token
        const response = await axios.post(`${baseURL}/auth/refresh-token`, {
          refreshToken,
        });

        if (response.data.succeeded) {
          // Save the new tokens
          Cookies.set('token', response.data.accessToken, { secure: true, sameSite: 'strict' });
          Cookies.set('refreshToken', response.data.refreshToken, { secure: true, sameSite: 'strict' });

          // Update the Authorization header
          originalRequest.headers.Authorization = `Bearer ${response.data.accessToken}`;

          // Retry the original request
          return apiClient(originalRequest);
        } else {
          // Refresh failed, redirect to home page
          Cookies.remove('token');
          Cookies.remove('refreshToken');
          window.location.href = '/';
          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Something went wrong with the refresh, redirect to home page
        Cookies.remove('token');
        Cookies.remove('refreshToken');
        window.location.href = '/';
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);
