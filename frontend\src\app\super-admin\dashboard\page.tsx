'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  Avatar,
  Stack,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Business as BusinessIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  Assignment as AssignmentIcon,
  BarChart as BarChartIcon,
  PersonAdd as PersonAddIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  MoreVert as MoreVertIcon,
  Visibility as VisibilityIcon,
} from '@mui/icons-material';
import Link from 'next/link';
import { getAllTenants } from '@/services/tenantService';
import { getSystemHealth } from '@/services/systemService';
import { getAggregateMetrics, getUserOverview } from '@/services/superAdminService';
import { TenantDto, TenantMetricsDto } from '@/types/tenants';

export default function SuperAdminDashboard() {
  const { user, isLoading, isAuthenticated } = useAuth();
  const [tenants, setTenants] = useState<TenantDto[]>([]);
  const [tenantsLoading, setTenantsLoading] = useState(true);
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [usersLoading, setUsersLoading] = useState(true);
  const [aggregateMetrics, setAggregateMetrics] = useState<any>(null);
  const [userOverview, setUserOverview] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      if (!user?.roles.includes('SuperAdmin')) {
        router.push('/dashboard');
      }
    }
  }, [isLoading, isAuthenticated, user, router]);

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        const data = await getAllTenants();
        setTenants(data);
      } catch (error) {
        console.error('Error fetching tenants:', error);
      } finally {
        setTenantsLoading(false);
      }
    };

    const fetchAggregateMetrics = async () => {
      try {
        const data = await getAggregateMetrics();
        if (data) {
          setAggregateMetrics(data);
        }
      } catch (error) {
        console.error('Error fetching aggregate metrics:', error);
      } finally {
        setMetricsLoading(false);
      }
    };

    const fetchUserOverview = async () => {
      try {
        const data = await getUserOverview();
        if (data) {
          setUserOverview(data);
        }
      } catch (error) {
        console.error('Error fetching user overview:', error);
      } finally {
        setUsersLoading(false);
      }
    };

    fetchTenants();
    fetchAggregateMetrics();
    fetchUserOverview();
  }, []);

  if (isLoading || !isAuthenticated || !user?.roles.includes('SuperAdmin')) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        SuperAdmin Dashboard
      </Typography>
      <Typography variant="subtitle1" color="text.secondary" gutterBottom>
        Overview of all tenants, usage metrics, and user statistics
      </Typography>

      {/* Tenants Overview Tile */}
      <Grid container spacing={3} sx={{ mt: 2, mb: 4 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }} elevation={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Tenants Overview
              </Typography>
              <Button 
                component={Link} 
                href="/super-admin/tenants"
                variant="outlined"
                startIcon={<VisibilityIcon />}
                size="small"
              >
                View All Tenants
              </Button>
            </Box>
            
            {tenantsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={4}>
                    <Card elevation={2}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            Total Tenants
                          </Typography>
                          <Typography variant="h4">
                            {tenants.length}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={2}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            Active Tenants
                          </Typography>
                          <Typography variant="h4">
                            {tenants.filter(t => t.isActive).length}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={2}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <BusinessIcon sx={{ fontSize: 40, color: 'error.main', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            Inactive Tenants
                          </Typography>
                          <Typography variant="h4">
                            {tenants.filter(t => !t.isActive).length}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom>
                  Recent Tenants
                </Typography>
                <List>
                  {tenants.slice(0, 5).map((tenant) => (
                    <ListItem
                      key={tenant.id}
                      component={Link}
                      href={`/super-admin/tenants/${tenant.id}`}
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': { bgcolor: 'action.hover' },
                        color: 'inherit',
                        textDecoration: 'none'
                      }}
                    >
                      <ListItemIcon>
                        <BusinessIcon color={tenant.isActive ? 'primary' : 'disabled'} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={tenant.name} 
                        secondary={`${tenant.userCount} users • ${tenant.isActive ? 'Active' : 'Inactive'} • Created: ${new Date(tenant.createdAt).toLocaleDateString()}`} 
                      />
                      <Chip 
                        label={tenant.isActive ? 'Active' : 'Inactive'} 
                        color={tenant.isActive ? 'success' : 'default'}
                        size="small"
                      />
                    </ListItem>
                  ))}
                </List>
              </>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Tenants Usage Metrics Tile */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }} elevation={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Tenants Usage Metrics
              </Typography>
              <Button 
                component={Link} 
                href="/super-admin/metrics"
                variant="outlined"
                startIcon={<BarChartIcon />}
                size="small"
              >
                Detailed Metrics
              </Button>
            </Box>
            
            {metricsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={3}>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={2}>
                    <CardContent>
                      <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                        Total Recognitions
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: 'primary.main' }}>
                        {aggregateMetrics.totalRecognitions.toLocaleString()}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                        <ArrowUpwardIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                        <Typography variant="body2" color="success.main">
                          {aggregateMetrics.recognitionGrowth}% from last month
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={2}>
                    <CardContent>
                      <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                        Total Points Issued
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: 'info.main' }}>
                        {aggregateMetrics.totalPointsIssued.toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={2}>
                    <CardContent>
                      <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                        Total Points Redeemed
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: 'warning.main' }}>
                        {aggregateMetrics.totalPointsRedeemed.toLocaleString()}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={12} md={6} lg={3}>
                  <Card elevation={2}>
                    <CardContent>
                      <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                        Avg. Redemption Rate
                      </Typography>
                      <Typography variant="h4" component="div" sx={{ color: 'success.main' }}>
                        {aggregateMetrics.averageRedemptionRate}%
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Tenants User Overview Tile */}
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }} elevation={2}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Tenants User Overview
              </Typography>
              <Button 
                component={Link} 
                href="/super-admin/users"
                variant="outlined"
                startIcon={<PeopleIcon />}
                size="small"
              >
                User Management
              </Button>
            </Box>
            
            {usersLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                <CircularProgress />
              </Box>
            ) : (
              <>
                <Grid container spacing={3} sx={{ mb: 3 }}>
                  <Grid item xs={12} md={4}>
                    <Card elevation={2}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <PeopleIcon sx={{ fontSize: 40, color: 'primary.main', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            Total Users
                          </Typography>
                          <Typography variant="h4">
                            {userOverview.totalUsers.toLocaleString()}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={2}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <PersonAddIcon sx={{ fontSize: 40, color: 'success.main', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            New Users This Month
                          </Typography>
                          <Typography variant="h4">
                            {userOverview.newUsersThisMonth}
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Card elevation={2}>
                      <CardContent sx={{ display: 'flex', alignItems: 'center' }}>
                        <TrendingUpIcon sx={{ fontSize: 40, color: 'info.main', mr: 2 }} />
                        <Box>
                          <Typography variant="h6" gutterBottom>
                            User Growth Rate
                          </Typography>
                          <Typography variant="h4">
                            {userOverview.userGrowthRate}%
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom>
                  Top Tenants by User Count
                </Typography>
                <List>
                  {userOverview.topTenantsByUsers.map((tenant, index) => (
                    <ListItem
                      key={tenant.id}
                      component={Link}
                      href={`/super-admin/tenants/${tenant.id}`}
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': { bgcolor: 'action.hover' },
                        color: 'inherit',
                        textDecoration: 'none'
                      }}
                    >
                      <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                        {index + 1}
                      </Avatar>
                      <ListItemText 
                        primary={tenant.name} 
                        secondary={`${tenant.userCount} users`} 
                      />
                    </ListItem>
                  ))}
                </List>
              </>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
}
